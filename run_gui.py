#!/usr/bin/env python3

"""
Simple launcher for OneShot-Extended GUI
"""

import os
import sys
import subprocess

def main():
    """Launch the OneShot-Extended GUI"""
    
    # Check if we're in the right directory
    if not os.path.exists('ose.py'):
        print("Error: ose.py not found in current directory.")
        print("Please run this script from the OneShot-Extended directory.")
        sys.exit(1)
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required.")
        sys.exit(1)
    
    # Check if running as root (required for OneShot-Extended)
    if os.getuid() != 0:
        print("Warning: OneShot-Extended requires root privileges.")
        print("The GUI will start, but attacks will fail without root access.")
        print("Consider running with: sudo python3 run_gui.py")
    
    # Launch the GUI
    try:
        subprocess.run([sys.executable, 'ose_gui.py'], check=True)
    except KeyboardInterrupt:
        print("\nGUI closed by user.")
    except subprocess.CalledProcessError as e:
        print(f"Error launching GUI: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
