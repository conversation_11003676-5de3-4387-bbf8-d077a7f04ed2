# OneShot-Extended GUI Package

This package contains a complete GUI for the OneShot-Extended WPS penetration testing utility.

## Contents:
- ose_gui.py - Main GUI application
- run_gui.py - Simple launcher
- test_gui.py - Test suite
- demo_interface_detection.py - Interface detection demo
- GUI_README.md - Complete documentation
- QUICK_START.md - Quick start guide
- ose.py - Original OneShot-Extended script
- src/ - Required source modules

## Quick Start:
1. Extract all files to a directory
2. Run: python test_gui.py (to test)
3. Run: python ose_gui.py (to use GUI)
4. Click "Test Mode" for testing without WiFi hardware

## Features:
- Cross-platform interface detection
- Test mode for systems without WiFi
- All OneShot-Extended options available
- Real-time output display
- Input validation

## Requirements:
- Python 3.9+
- tkinter (usually included)
- Root/Admin privileges for actual attacks

For detailed instructions, see GUI_README.md and QUICK_START.md
