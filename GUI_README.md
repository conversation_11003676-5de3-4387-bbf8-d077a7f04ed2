# OneShot-Extended GUI

A graphical user interface for the OneShot-Extended WPS penetration testing utility.

## Features

- **User-friendly interface** with tabbed layout for easy navigation
- **All command-line options** available through the GUI
- **Real-time output** display with scrollable text area
- **Interface detection** to automatically find wireless adapters
- **Attack progress monitoring** with start/stop controls
- **Input validation** to prevent common configuration errors

## Requirements

- Python 3.9 or higher
- tkinter (usually included with Python)
- All OneShot-Extended dependencies (pixiewps, etc.)
- Root/Administrator privileges (required for wireless operations)

## Installation

No additional installation required. The GUI uses only Python standard library modules.

## Usage

### Method 1: Direct Launch
```bash
# On Linux/macOS
sudo python3 ose_gui.py

# On Windows (run as Administrator)
python ose_gui.py
```

### Method 2: Using the Launcher
```bash
# On Linux/macOS
sudo python3 run_gui.py

# On Windows (run as Administrator)
python run_gui.py
```

## GUI Layout

### Basic Settings Tab
- **Interface**: Select your wireless interface (use "Detect" button for auto-detection)
- **Target Settings**: Specify BSSID and PIN (optional)
- **Attack Methods**: Choose between Pixie Dust, Bruteforce, or PBC attacks
- **Attack Options**: Configure specific attack parameters

### Advanced Settings Tab
- **Output Options**: Control credential saving and verbosity
- **Interface Options**: Configure interface behavior and driver support
- **Scan Options**: Set loop mode, screen clearing, and scan order
- **Vulnerable Devices List**: Specify custom vulnerability database

### Output Tab
- **Real-time output**: View attack progress and results
- **Status bar**: Monitor current operation status
- **Control buttons**: Start, stop, and clear operations

## Common Usage Scenarios

### 1. Quick Pixie Dust Attack
1. Set your wireless interface (e.g., `wlan0`)
2. Check "Pixie Dust Attack"
3. Leave BSSID empty to scan for targets
4. Click "Start Attack"

### 2. Targeted Attack with Known BSSID
1. Set your wireless interface
2. Enter the target BSSID
3. Choose your attack method
4. Click "Start Attack"

### 3. Bruteforce Attack with Delay
1. Set your wireless interface
2. Check "Bruteforce Attack"
3. Set delay between attempts (e.g., `1.5` seconds)
4. Click "Start Attack"

### 4. Loop Mode for Multiple Targets
1. Configure your basic settings
2. Go to Advanced Settings tab
3. Check "Run in loop mode"
4. Click "Start Attack"

## Important Notes

### Security and Legal Considerations
- **Only use on networks you own or have explicit permission to test**
- This tool is for educational and authorized penetration testing purposes only
- Unauthorized access to wireless networks is illegal in most jurisdictions

### Root Privileges
- OneShot-Extended requires root/administrator privileges to access wireless interfaces
- The GUI will warn you if not running with sufficient privileges
- On Linux: Use `sudo` to run the GUI
- On Windows: Run Command Prompt/PowerShell as Administrator

### Interface Requirements
- Your wireless adapter must support monitor mode
- Some adapters may require specific drivers or firmware
- USB wireless adapters often work better than built-in ones for penetration testing

## Troubleshooting

### "Interface not found" Error
- Ensure your wireless adapter is connected and recognized by the system
- Use the "Detect" button to automatically find available interfaces
- Check that your adapter supports monitor mode

### "Permission denied" Error
- Make sure you're running the GUI with root/administrator privileges
- On Linux: `sudo python3 ose_gui.py`
- On Windows: Run as Administrator

### "pixiewps not found" Error
- Install pixiewps: `sudo apt-get install pixiewps` (Ubuntu/Debian)
- Ensure pixiewps is in your system PATH

### GUI Not Starting
- Check Python version: `python3 --version` (must be 3.9+)
- Ensure tkinter is installed: `python3 -c "import tkinter"`
- Try running from the OneShot-Extended directory

## Command Line Equivalent

The GUI generates command-line arguments that correspond to the original OneShot-Extended script. For example:

**GUI Settings:**
- Interface: wlan0
- Pixie Dust Attack: ✓
- Verbose: ✓

**Generated Command:**
```bash
python3 ose.py -i wlan0 -K -v
```

## Support

For issues specific to the GUI, check:
1. Python and tkinter installation
2. File permissions and location
3. Root/administrator privileges

For issues with OneShot-Extended functionality, refer to the main project documentation.

## License

This GUI follows the same license as OneShot-Extended (GNU General Public License v2.0).
