#!/usr/bin/env python3

"""
Demo script to show interface detection functionality
"""

import os
import subprocess
import sys

def demo_windows_detection():
    """Demo Windows interface detection"""
    print("=== Windows Interface Detection Demo ===")
    
    try:
        # Try netsh command
        print("1. Trying 'netsh wlan show interfaces'...")
        result = subprocess.run(['netsh', 'wlan', 'show', 'interfaces'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ Command executed successfully")
            print("Output:")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
            
            # Parse interfaces
            interfaces = []
            for line in result.stdout.split('\n'):
                line = line.strip()
                if 'Name' in line and ':' in line:
                    name = line.split(':', 1)[1].strip()
                    if name and name not in interfaces:
                        interfaces.append(name)
            
            print(f"Detected interfaces: {interfaces}")
        else:
            print(f"✗ Command failed with return code: {result.returncode}")
            print(f"Error: {result.stderr}")
            
    except FileNotFoundError:
        print("✗ netsh command not found")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print()

def demo_linux_detection():
    """Demo Linux interface detection"""
    print("=== Linux Interface Detection Demo ===")
    
    # Method 1: ip link show
    try:
        print("1. Trying 'ip link show'...")
        result = subprocess.run(['ip', 'link', 'show'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Command executed successfully")
            interfaces = []
            for line in result.stdout.split('\n'):
                if ': ' in line and ('wl' in line or 'wlan' in line):
                    interface = line.split(': ')[1].split('@')[0]
                    if interface not in interfaces:
                        interfaces.append(interface)
            print(f"Found wireless interfaces: {interfaces}")
        else:
            print(f"✗ Command failed: {result.stderr}")
            
    except FileNotFoundError:
        print("✗ 'ip' command not found (not on Linux)")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    # Method 2: /proc/net/wireless
    try:
        print("2. Trying '/proc/net/wireless'...")
        with open('/proc/net/wireless', 'r') as f:
            interfaces = []
            for line in f:
                if ':' in line and not line.strip().startswith('Inter'):
                    interface = line.split(':')[0].strip()
                    if interface and interface not in interfaces:
                        interfaces.append(interface)
            print(f"Found in /proc/net/wireless: {interfaces}")
    except FileNotFoundError:
        print("✗ /proc/net/wireless not found (not on Linux)")
    except Exception as e:
        print(f"✗ Error reading /proc/net/wireless: {e}")
    
    # Method 3: iwconfig
    try:
        print("3. Trying 'iwconfig'...")
        result = subprocess.run(['iwconfig'], capture_output=True, text=True, stderr=subprocess.DEVNULL)
        
        if result.returncode == 0:
            interfaces = []
            for line in result.stdout.split('\n'):
                if 'IEEE 802.11' in line:
                    interface = line.split()[0]
                    if interface and interface not in interfaces:
                        interfaces.append(interface)
            print(f"Found with iwconfig: {interfaces}")
        else:
            print("✗ iwconfig failed or no wireless interfaces")
            
    except FileNotFoundError:
        print("✗ iwconfig command not found")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    print()

def demo_test_interfaces():
    """Demo test interface functionality"""
    print("=== Test Interface Demo ===")
    print("When no real interfaces are found, the GUI offers these test interfaces:")
    
    test_interfaces = ['wlan0', 'wlan1', 'wlp3s0', 'wlx001122334455', 'ath0', 'ra0']
    
    for i, interface in enumerate(test_interfaces, 1):
        print(f"{i}. {interface}")
    
    print("\nThese are common wireless interface names used for testing.")
    print("They allow you to test the GUI functionality without real hardware.")
    print()

def main():
    """Main demo function"""
    print("OneShot-Extended GUI - Interface Detection Demo")
    print("=" * 50)
    print()
    
    print(f"Operating System: {os.name}")
    print(f"Platform: {sys.platform}")
    print()
    
    if os.name == 'nt':
        demo_windows_detection()
    else:
        demo_linux_detection()
    
    demo_test_interfaces()
    
    print("=== Summary ===")
    print("The GUI interface detection works as follows:")
    print("1. Try to detect real wireless interfaces using OS-specific commands")
    print("2. If no interfaces found, offer test interfaces for GUI testing")
    print("3. User can select from available interfaces")
    print("4. Test mode allows full GUI testing without real hardware")
    print()
    print("To test the GUI:")
    print("1. Run: python ose_gui.py")
    print("2. Click 'Test Mode' button")
    print("3. Select a test interface")
    print("4. Configure attack options")
    print("5. Click 'Start Attack' to see test output")

if __name__ == '__main__':
    main()
