#!/usr/bin/env python3

"""
Test script for OneShot-Extended GUI
This script tests the GUI functionality without requiring actual wireless interfaces.
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_gui_import():
    """Test if the GUI can be imported successfully"""
    try:
        import ose_gui
        print("✓ GUI module imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import GUI module: {e}")
        return False

def test_tkinter():
    """Test if tkinter is available and working"""
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the window
        root.destroy()
        print("✓ Tkinter is working")
        return True
    except Exception as e:
        print(f"✗ Tkinter test failed: {e}")
        return False

def test_interface_detection():
    """Test the interface detection functionality"""
    try:
        import ose_gui
        
        # Create a temporary GUI instance for testing
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        gui = ose_gui.OneShotGUI(root)
        
        # Test Windows interface detection
        print("Testing Windows interface detection...")
        windows_interfaces = gui._detect_windows_interfaces()
        print(f"  Found {len(windows_interfaces)} Windows interfaces: {windows_interfaces}")
        
        # Test Linux interface detection  
        print("Testing Linux interface detection...")
        linux_interfaces = gui._detect_linux_interfaces()
        print(f"  Found {len(linux_interfaces)} Linux interfaces: {linux_interfaces}")
        
        root.destroy()
        print("✓ Interface detection test completed")
        return True
        
    except Exception as e:
        print(f"✗ Interface detection test failed: {e}")
        return False

def test_command_building():
    """Test the command building functionality"""
    try:
        import ose_gui
        
        root = tk.Tk()
        root.withdraw()
        
        gui = ose_gui.OneShotGUI(root)
        gui.test_mode = True
        
        # Set some test values
        gui.interface_var.set("wlan0")
        gui.bssid_var.set("00:11:22:33:44:55")
        gui.pixie_dust_var.set(True)
        gui.verbose_var.set(True)
        
        # Test command building
        cmd = gui.build_command()
        expected_parts = ['python3', 'ose.py', '-i', 'wlan0', '-b', '00:11:22:33:44:55', '-K', '-v']
        
        print(f"Built command: {' '.join(cmd)}")
        
        # Check if all expected parts are in the command
        for part in expected_parts:
            if part not in cmd:
                print(f"✗ Missing expected part: {part}")
                return False
        
        root.destroy()
        print("✓ Command building test passed")
        return True
        
    except Exception as e:
        print(f"✗ Command building test failed: {e}")
        return False

def run_gui_test():
    """Run the GUI in test mode"""
    try:
        print("Starting GUI in test mode...")
        print("Instructions:")
        print("1. Click 'Test Mode' button to enable test interfaces")
        print("2. Select an interface from the list")
        print("3. Configure some attack options")
        print("4. Click 'Start Attack' to see the test output")
        print("5. Close the GUI when done testing")
        
        import ose_gui
        root = tk.Tk()
        app = ose_gui.OneShotGUI(root)
        
        # Show a message about test mode
        root.after(1000, lambda: messagebox.showinfo(
            "Test Mode Available", 
            "Click the 'Test Mode' button to enable test interfaces\n"
            "for GUI testing without real wireless hardware."
        ))
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return False

def main():
    """Main test function"""
    print("OneShot-Extended GUI Test Suite")
    print("=" * 40)
    
    tests = [
        ("Python Version", lambda: sys.version_info >= (3, 9)),
        ("Tkinter Availability", test_tkinter),
        ("GUI Import", test_gui_import),
        ("Interface Detection", test_interface_detection),
        ("Command Building", test_command_building),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} test failed")
        except Exception as e:
            print(f"✗ {test_name} test error: {e}")
    
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! GUI should work correctly.")
        
        response = input("\nWould you like to run the GUI in test mode? (y/n): ")
        if response.lower() == 'y':
            run_gui_test()
    else:
        print("✗ Some tests failed. Please check the errors above.")

if __name__ == '__main__':
    main()
