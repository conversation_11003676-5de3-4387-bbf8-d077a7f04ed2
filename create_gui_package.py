#!/usr/bin/env python3

"""
Create a package of OneShot-Extended GUI files for easy sharing/backup
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_gui_package():
    """Create a ZIP package with all GUI-related files"""
    
    # Files to include in the package
    gui_files = [
        'ose_gui.py',
        'run_gui.py', 
        'test_gui.py',
        'demo_interface_detection.py',
        'GUI_README.md',
        'QUICK_START.md',
        'ose.py',  # Original script
        'src/args.py',  # Dependencies
        'src/utils.py',
        'vulnwsc.txt'
    ]
    
    # Create package name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"OneShot-Extended-GUI_{timestamp}.zip"
    
    print(f"Creating GUI package: {package_name}")
    
    with zipfile.ZipFile(package_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in gui_files:
            if os.path.exists(file_path):
                zipf.write(file_path)
                print(f"✓ Added: {file_path}")
            else:
                print(f"✗ Missing: {file_path}")
        
        # Add src directory structure
        for root, dirs, files in os.walk('src'):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    zipf.write(file_path)
                    print(f"✓ Added: {file_path}")
    
    print(f"\n✅ Package created: {package_name}")
    print(f"📁 Size: {os.path.getsize(package_name)} bytes")
    
    return package_name

def create_readme_for_package():
    """Create a README for the package"""
    
    readme_content = """# OneShot-Extended GUI Package

This package contains a complete GUI for the OneShot-Extended WPS penetration testing utility.

## Contents:
- ose_gui.py - Main GUI application
- run_gui.py - Simple launcher
- test_gui.py - Test suite
- demo_interface_detection.py - Interface detection demo
- GUI_README.md - Complete documentation
- QUICK_START.md - Quick start guide
- ose.py - Original OneShot-Extended script
- src/ - Required source modules

## Quick Start:
1. Extract all files to a directory
2. Run: python test_gui.py (to test)
3. Run: python ose_gui.py (to use GUI)
4. Click "Test Mode" for testing without WiFi hardware

## Features:
- Cross-platform interface detection
- Test mode for systems without WiFi
- All OneShot-Extended options available
- Real-time output display
- Input validation

## Requirements:
- Python 3.9+
- tkinter (usually included)
- Root/Admin privileges for actual attacks

For detailed instructions, see GUI_README.md and QUICK_START.md
"""
    
    with open('PACKAGE_README.txt', 'w') as f:
        f.write(readme_content)
    
    print("✓ Created PACKAGE_README.txt")

if __name__ == '__main__':
    print("OneShot-Extended GUI Package Creator")
    print("=" * 40)
    
    create_readme_for_package()
    package_file = create_gui_package()
    
    print(f"\n🎉 Package ready for sharing!")
    print(f"📦 File: {package_file}")
    print(f"📋 Instructions: PACKAGE_README.txt")
    print(f"\nYou can now:")
    print(f"1. Upload {package_file} to file sharing services")
    print(f"2. Email the package to others")
    print(f"3. Store as backup")
    print(f"4. Upload to GitHub manually")
